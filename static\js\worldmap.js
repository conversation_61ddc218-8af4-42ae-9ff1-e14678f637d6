/**
 * World Map Visualization for C2 Dashboard
 * Displays connected clients on a dark-themed world map
 */

let worldMap = null;
let clientMarkers = [];
let markerClusterGroup = null;

/**
 * Initialize the world map
 */
function initializeWorldMap() {
    // Check if map container exists and is visible
    const mapContainer = document.getElementById('worldMap');
    if (!mapContainer) {
        console.warn('World map container not found');
        return;
    }

    // Check if container is visible (has dimensions)
    if (mapContainer.offsetWidth === 0 || mapContainer.offsetHeight === 0) {
        console.warn('World map container not visible, retrying...');
        setTimeout(initializeWorldMap, 500);
        return;
    }

    // Destroy existing map if it exists
    if (worldMap) {
        worldMap.remove();
        worldMap = null;
    }

    try {
        // Check if Leaflet is loaded
        if (typeof L === 'undefined') {
            throw new Error('Leaflet library not loaded');
        }

        console.log('Initializing Leaflet map...');

        // Initialize the map with dark theme
        worldMap = L.map('worldMap', {
            center: [20, 0],
            zoom: 2,
            zoomControl: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            boxZoom: true,
            keyboard: true,
            dragging: true,
            touchZoom: true,
            attributionControl: false
        });

        // Add dark tile layer
        L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            attribution: '',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(worldMap);

        // Initialize marker cluster group if available
        if (typeof L.markerClusterGroup === 'function') {
            markerClusterGroup = L.markerClusterGroup({
                iconCreateFunction: function(cluster) {
                    const count = cluster.getChildCount();
                    let className = 'marker-cluster-small';

                    if (count > 10) {
                        className = 'marker-cluster-large';
                    } else if (count > 5) {
                        className = 'marker-cluster-medium';
                    }

                    return L.divIcon({
                        html: '<div><span>' + count + '</span></div>',
                        className: 'marker-cluster ' + className,
                        iconSize: L.point(40, 40)
                    });
                },
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: false,
                zoomToBoundsOnClick: true
            });
            worldMap.addLayer(markerClusterGroup);
        } else {
            console.warn('MarkerCluster plugin not available, using layer group instead');
            markerClusterGroup = L.layerGroup();
            worldMap.addLayer(markerClusterGroup);
        }

        // Add custom CSS for dark theme markers
        addMapStyles();

        // Load initial client locations
        updateClientLocations();

        console.log('World map initialized successfully');
    } catch (error) {
        console.error('Error initializing world map:', error);
        // Show fallback content with client count
        mapContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                <div class="text-center">
                    <i class="bi bi-globe fs-1 mb-3 text-danger"></i>
                    <div class="text-white">World Map</div>
                    <small>Map visualization unavailable</small>
                    <div class="mt-3">
                        <div class="badge bg-danger" id="fallbackClientCount">0 Clients</div>
                    </div>
                </div>
            </div>
        `;

        // Update fallback with client count
        updateFallbackClientCount();
    }
}

/**
 * Add custom styles for the map
 */
function addMapStyles() {
    if (document.getElementById('mapStyles')) return;

    const style = document.createElement('style');
    style.id = 'mapStyles';
    style.textContent = `
        .marker-cluster-small {
            background-color: rgba(220, 53, 69, 0.6);
            border: 2px solid rgba(220, 53, 69, 0.8);
        }
        .marker-cluster-medium {
            background-color: rgba(220, 53, 69, 0.7);
            border: 2px solid rgba(220, 53, 69, 0.9);
        }
        .marker-cluster-large {
            background-color: rgba(220, 53, 69, 0.8);
            border: 2px solid rgba(220, 53, 69, 1);
        }
        .marker-cluster {
            border-radius: 50%;
            text-align: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .marker-cluster div {
            border-radius: 50%;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .client-marker {
            background-color: #dc3545;
            border: 2px solid #ffffff;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.6);
        }
        .leaflet-popup-content-wrapper {
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            border: 1px solid #dc3545;
        }
        .leaflet-popup-tip {
            background-color: rgba(0, 0, 0, 0.9);
            border: 1px solid #dc3545;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Update client locations on the map
 */
async function updateClientLocations() {
    if (!worldMap || !markerClusterGroup) return;

    // Clear existing markers
    markerClusterGroup.clearLayers();
    clientMarkers = [];

    try {
        // Fetch current clients
        const response = await fetch('/api/clients');
        const clients = await response.json();

        console.log(`Updating map with ${clients.length} clients...`);

        // Add clients to map with real geolocation (process sequentially to avoid rate limits)
        for (const client of clients) {
            await addClientToMap(client);
            // Small delay to avoid overwhelming geolocation services
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Update statistics after all clients are processed
        updateMapStatistics(clients);

        // Update fallback if map failed to load
        updateFallbackClientCount();

        console.log(`Map updated with ${clientMarkers.length} client markers`);

    } catch (error) {
        console.error('Error fetching clients for map:', error);
        updateFallbackClientCount();
    }
}

/**
 * Add a client marker to the map
 */
async function addClientToMap(client) {
    try {
        // Get real location from IP geolocation
        const location = await getLocationFromIP(client);

        if (location && location.lat !== undefined && location.lng !== undefined) {
            const marker = L.circleMarker([location.lat, location.lng], {
                radius: 8,
                fillColor: '#dc3545',
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8,
                className: 'client-marker'
            });

            // Add popup with enhanced client information including real location
            const popupContent = `
                <div class="client-popup">
                    <h6 class="text-danger mb-2">
                        <i class="bi bi-${client.os_icon || 'laptop'} me-1"></i>
                        ${client.hostname || `Client ${client.id}`}
                    </h6>
                    <div class="mb-1">
                        <strong>OS:</strong> ${client.os || 'Unknown'} ${client.os_version || ''}
                    </div>
                    <div class="mb-1">
                        <strong>User:</strong> ${client.username || 'Unknown'}
                    </div>
                    <div class="mb-1">
                        <strong>Public IP:</strong> ${client.public_ip || 'Unknown'}
                    </div>
                    <div class="mb-1">
                        <strong>Local IP:</strong> ${client.address}
                    </div>
                    <div class="mb-1">
                        <strong>Location:</strong> ${location.city}, ${location.country}
                        ${location.region ? `, ${location.region}` : ''}
                    </div>
                    ${location.timezone ? `<div class="mb-1"><strong>Timezone:</strong> ${location.timezone}</div>` : ''}
                    <div class="mb-1">
                        <strong>Coordinates:</strong> ${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}
                    </div>
                    ${location.source ? `<div class="mb-1"><small class="text-muted">Location source: ${location.source}</small></div>` : ''}
                    <div class="mb-1">
                        <strong>Connected:</strong> ${client.connected_at}
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-danger me-1" onclick="selectClientFromMap(${client.id})">
                            <i class="bi bi-cursor me-1"></i>Select
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="showClientDetails(${client.id})">
                            <i class="bi bi-info-circle me-1"></i>Details
                        </button>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent);

            // Add to cluster group
            markerClusterGroup.addLayer(marker);
            clientMarkers.push({
                marker: marker,
                client: client,
                location: location
            });

            console.log(`Added client ${client.id} to map at ${location.city}, ${location.country} (${location.lat}, ${location.lng})`);
        } else {
            console.warn(`Could not determine location for client ${client.id}`);
        }
    } catch (error) {
        console.error(`Error adding client ${client.id} to map:`, error);
    }
}

/**
 * Get real location from client data or IP address using geolocation API
 */
async function getLocationFromIP(client) {
    // Priority 1: Use client-provided GPS/location data if available
    if (client.latitude && client.longitude) {
        console.log(`Using client-provided location for ${client.hostname}: ${client.city}, ${client.country}`);
        return {
            lat: client.latitude,
            lng: client.longitude,
            city: client.city || 'Unknown',
            country: client.country || 'Unknown',
            region: client.region || '',
            timezone: client.timezone || '',
            source: client.location_source || 'Client-provided'
        };
    }

    // Priority 2: Use public IP if available, otherwise fall back to local IP
    const ip = client.public_ip && client.public_ip !== 'Unknown' ? client.public_ip : client.address.split(':')[0];

    // Handle local/private IPs with fallback locations
    const localIPs = ['127.0.0.1', 'localhost', '::1'];
    const privateIPRanges = [
        /^192\.168\./,
        /^10\./,
        /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
        /^169\.254\./
    ];

    const isLocalIP = localIPs.includes(ip);
    const isPrivateIP = privateIPRanges.some(range => range.test(ip));

    if (isLocalIP || isPrivateIP) {
        // For local/private IPs, try to get location from a different approach
        return await getLocationForLocalIP(client);
    }

    try {
        // Try multiple free geolocation services
        const services = [
            {
                url: `http://ip-api.com/json/${ip}?fields=status,message,country,countryCode,region,regionName,city,lat,lon,timezone`,
                parser: (data) => {
                    if (data.status === 'success') {
                        return {
                            lat: data.lat,
                            lng: data.lon,
                            city: data.city || 'Unknown',
                            country: data.country || 'Unknown',
                            region: data.regionName || '',
                            timezone: data.timezone || ''
                        };
                    }
                    return null;
                }
            },
            {
                url: `https://ipapi.co/${ip}/json/`,
                parser: (data) => {
                    if (data.latitude && data.longitude) {
                        return {
                            lat: data.latitude,
                            lng: data.longitude,
                            city: data.city || 'Unknown',
                            country: data.country_name || 'Unknown',
                            region: data.region || '',
                            timezone: data.timezone || ''
                        };
                    }
                    return null;
                }
            },
            {
                url: `https://ipinfo.io/${ip}/json`,
                parser: (data) => {
                    if (data.loc) {
                        const [lat, lng] = data.loc.split(',').map(Number);
                        return {
                            lat: lat,
                            lng: lng,
                            city: data.city || 'Unknown',
                            country: data.country || 'Unknown',
                            region: data.region || '',
                            timezone: data.timezone || ''
                        };
                    }
                    return null;
                }
            }
        ];

        for (const service of services) {
            try {
                console.log(`Trying geolocation service for IP ${ip}:`, service.url);
                const response = await fetch(service.url, {
                    method: 'GET',
                    timeout: 5000
                });

                if (response.ok) {
                    const data = await response.json();
                    const location = service.parser(data);

                    if (location) {
                        console.log(`Successfully got location for ${ip}:`, location);
                        return location;
                    }
                }
            } catch (error) {
                console.warn(`Geolocation service failed for ${ip}:`, error);
                continue;
            }
        }

        // If all services fail, return fallback
        return getFallbackLocation(client);

    } catch (error) {
        console.error(`Error getting location for IP ${ip}:`, error);
        return getFallbackLocation(client);
    }
}

/**
 * Get location for local/private IPs using alternative methods
 */
async function getLocationForLocalIP(client) {
    try {
        // Try to get user's location using browser geolocation API (if available)
        if (navigator.geolocation) {
            return new Promise((resolve) => {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        resolve({
                            lat: position.coords.latitude,
                            lng: position.coords.longitude,
                            city: 'Local Network',
                            country: 'Local',
                            region: 'Private Network',
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                        });
                    },
                    () => {
                        // Geolocation failed, use fallback
                        resolve(getFallbackLocation(client));
                    },
                    { timeout: 5000 }
                );
            });
        }
    } catch (error) {
        console.warn('Browser geolocation not available:', error);
    }

    return getFallbackLocation(client);
}

/**
 * Get fallback location based on client information
 */
function getFallbackLocation(client) {
    // Use timezone or other client info to make educated guess
    const fallbackLocations = {
        'Windows': { lat: 39.8283, lng: -98.5795, city: 'Geographic Center', country: 'USA' }, // US center
        'Linux': { lat: 61.5240, lng: 105.3188, city: 'Geographic Center', country: 'Russia' }, // Russia center
        'macOS': { lat: 37.0902, lng: -95.7129, city: 'Geographic Center', country: 'USA' }, // US center
        'Unknown': { lat: 0, lng: 0, city: 'Unknown', country: 'Unknown' }
    };

    const os = client.os || 'Unknown';
    const location = fallbackLocations[os] || fallbackLocations['Unknown'];

    // Add some randomization to avoid all clients appearing at the same spot
    const randomOffset = 5; // degrees
    location.lat += (Math.random() - 0.5) * randomOffset;
    location.lng += (Math.random() - 0.5) * randomOffset;

    return location;
}

/**
 * Update map statistics
 */
function updateMapStatistics(clients) {
    // Count unique countries
    const countries = new Set();
    clientMarkers.forEach(marker => {
        if (marker.location.country !== 'Unknown') {
            countries.add(marker.location.country);
        }
    });
    
    // Update dashboard stats
    const totalClientsElement = document.getElementById('totalClients');
    const totalCountriesElement = document.getElementById('totalCountries');
    
    if (totalClientsElement) {
        totalClientsElement.textContent = clients.length;
    }
    
    if (totalCountriesElement) {
        totalCountriesElement.textContent = countries.size;
    }
}

/**
 * Select a client from the map
 */
function selectClientFromMap(clientId) {
    // Switch to commands page and select the client
    if (typeof selectClient === 'function') {
        selectClient(clientId);
    }
    
    // Navigate to commands page
    if (typeof navigateToPage === 'function') {
        navigateToPage('commands');
    }
    
    // Close any open popups
    if (worldMap) {
        worldMap.closePopup();
    }
}

/**
 * Resize map when container changes
 */
function resizeMap() {
    if (worldMap) {
        setTimeout(() => {
            worldMap.invalidateSize();
        }, 100);
    }
}

/**
 * Focus map on a specific client
 */
function focusOnClient(clientId) {
    const clientMarker = clientMarkers.find(m => m.client.id === clientId);
    if (clientMarker && worldMap) {
        worldMap.setView([clientMarker.location.lat, clientMarker.location.lng], 8);
        clientMarker.marker.openPopup();
    }
}

/**
 * Update fallback client count when map fails to load
 */
function updateFallbackClientCount() {
    const fallbackElement = document.getElementById('fallbackClientCount');
    if (fallbackElement) {
        // Get client count from global clients array or fetch from API
        let clientCount = 0;
        if (typeof window.clients !== 'undefined' && Array.isArray(window.clients)) {
            clientCount = window.clients.length;
        }
        fallbackElement.textContent = `${clientCount} Client${clientCount !== 1 ? 's' : ''}`;
    }
}

// Export functions for global access
window.initializeWorldMap = initializeWorldMap;
window.updateClientLocations = updateClientLocations;
window.selectClientFromMap = selectClientFromMap;
window.resizeMap = resizeMap;
window.focusOnClient = focusOnClient;
window.updateFallbackClientCount = updateFallbackClientCount;
