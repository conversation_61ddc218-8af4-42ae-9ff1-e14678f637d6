/* Sidebar Styles for C2 Dashboard */

:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
}

/* Sidebar Container */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(13, 13, 13, 0.95) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-right: 2px solid var(--c2-red);
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--c2-border-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h4 {
    color: var(--c2-white);
    margin: 0;
    font-size: 1.2rem;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-header h4 {
    opacity: 0;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--c2-text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.nav-link:hover {
    color: var(--c2-white);
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--c2-red);
}

.nav-link.active {
    color: var(--c2-white);
    background-color: rgba(220, 53, 69, 0.2);
    border-left-color: var(--c2-red);
    box-shadow: inset 0 0 10px rgba(220, 53, 69, 0.3);
}

.nav-link i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    margin-right: 0.75rem;
    color: var(--c2-red);
    transition: all 0.3s ease;
}

.nav-link span {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid var(--c2-border-gray);
    background: rgba(0, 0, 0, 0.3);
}

.server-status-mini {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.server-status-mini small {
    color: var(--c2-text-muted);
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .server-status-mini small {
    opacity: 0;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    padding: 2rem;
    transition: margin-left 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Mobile Sidebar Toggle */
.mobile-sidebar-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    border: 1px solid var(--c2-red);
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

/* Page Content */
.page-content {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.page-content.active {
    display: block;
}

/* World Map Container */
.world-map-container {
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--c2-border-gray);
    background: var(--c2-black);
    position: relative;
}

#worldMap {
    height: 100% !important;
    width: 100% !important;
    filter: brightness(0.7) contrast(1.2) hue-rotate(0deg);
    z-index: 1;
}

/* Ensure Leaflet map displays properly */
.leaflet-container {
    height: 100% !important;
    width: 100% !important;
    background: #000 !important;
}

/* Quick Commands Grid */
.quick-commands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.quick-command-btn {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--c2-red);
    color: var(--c2-white);
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
    cursor: pointer;
}

.quick-command-btn:hover {
    background: rgba(220, 53, 69, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.quick-command-btn i {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar.collapsed + .main-content {
        margin-left: 0;
    }
    
    .mobile-sidebar-toggle {
        display: block;
    }
    
    .quick-commands-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (min-width: 769px) {
    .mobile-sidebar-toggle {
        display: none;
    }
}

/* Scrollbar Styling for Sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--c2-red);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--c2-red-bright);
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .nav-link {
    position: relative;
}

.sidebar.collapsed .nav-link::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: var(--c2-white);
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1001;
    margin-left: 0.5rem;
    border: 1px solid var(--c2-red);
}

.sidebar.collapsed .nav-link:hover::after {
    opacity: 1;
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--c2-red);
}

.page-header h1 {
    color: var(--c2-white);
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.page-header p {
    color: var(--c2-text-muted);
    margin: 0.5rem 0 0 0;
}
