<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SS NETWORK - C2 Dashboard</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Leaflet CSS for World Map -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet MarkerCluster CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

    <!-- Custom CSS -->
    <link href="/static/css/theme.css" rel="stylesheet">
    <link href="/static/css/animations.css" rel="stylesheet">
    <link href="/static/css/sidebar.css" rel="stylesheet">
</head>
<body>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="bi bi-shield-exclamation text-danger me-2"></i>SS NETWORK</h4>
            <button class="btn btn-sm btn-outline-light d-md-none" id="sidebarToggle">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <a href="#" class="nav-link active" data-page="dashboard" data-tooltip="Dashboard">
                <i class="bi bi-speedometer2"></i>
                <span>Dashboard</span>
            </a>
            <a href="#" class="nav-link" data-page="clients" data-tooltip="Client Management">
                <i class="bi bi-people"></i>
                <span>Client Management</span>
            </a>
            <a href="#" class="nav-link" data-page="commands" data-tooltip="Command Center">
                <i class="bi bi-terminal"></i>
                <span>Command Center</span>
            </a>
            <a href="#" class="nav-link" data-page="logs" data-tooltip="Activity Logs">
                <i class="bi bi-journal-text"></i>
                <span>Activity Logs</span>
            </a>
            <a href="#" class="nav-link" data-page="settings" data-tooltip="Settings">
                <i class="bi bi-gear"></i>
                <span>Settings</span>
            </a>
        </nav>

        <div class="sidebar-footer">
            <div class="server-status-mini">
                <span class="status-indicator" id="sidebarStatusIndicator"></span>
                <small id="sidebarStatusText">Loading...</small>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="btn btn-outline-light d-md-none mobile-sidebar-toggle" id="mobileSidebarToggle">
        <i class="bi bi-list"></i>
    </button>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">

        <!-- Dashboard Page -->
        <div class="page-content active" id="dashboardPage">
            <div class="page-header">
                <h1><i class="bi bi-speedometer2 me-3"></i>Command & Control Dashboard</h1>
                <p>Monitor server status, connected clients, and global operations</p>
            </div>

            <!-- Server Status Bar -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <span class="status-indicator me-2" id="serverStatusIndicator"></span>
                                        <span id="serverStatusText" class="fw-bold">C2 Server Status: Loading...</span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-success btn-sm" id="startBtn" onclick="startServer()" disabled>
                                            <i class="bi bi-play-fill me-1"></i>Start Server
                                        </button>
                                        <button class="btn btn-danger btn-sm" id="stopBtn" onclick="stopServer()" disabled>
                                            <i class="bi bi-stop-fill me-1"></i>Stop Server
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" onclick="refreshDashboard()">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-dark border-secondary text-center">
                        <div class="card-body">
                            <i class="bi bi-people fs-1 text-danger mb-2"></i>
                            <h3 id="totalClients" class="text-white">0</h3>
                            <p class="text-muted mb-0">Connected Clients</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-dark border-secondary text-center">
                        <div class="card-body">
                            <i class="bi bi-globe fs-1 text-danger mb-2"></i>
                            <h3 id="totalCountries" class="text-white">0</h3>
                            <p class="text-muted mb-0">Countries</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-dark border-secondary text-center">
                        <div class="card-body">
                            <i class="bi bi-clock fs-1 text-danger mb-2"></i>
                            <h3 id="serverUptime" class="text-white">--:--:--</h3>
                            <p class="text-muted mb-0">Server Uptime</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-dark border-secondary text-center">
                        <div class="card-body">
                            <i class="bi bi-activity fs-1 text-danger mb-2"></i>
                            <h3 id="commandsExecuted" class="text-white">0</h3>
                            <p class="text-muted mb-0">Commands Executed</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Operating Systems Breakdown -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-pc-display text-danger me-2"></i>
                                Operating Systems Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="osBreakdown" class="row">
                                <div class="col-12 text-center text-muted">
                                    <i class="bi bi-pc-display fs-1 mb-3"></i>
                                    <div>No clients connected</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- World Map and Quick Commands -->
            <div class="row g-4">
                <!-- World Map -->
                <div class="col-lg-8">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-globe text-danger me-2"></i>
                                Global Client Distribution
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="world-map-container">
                                <div id="worldMap"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="col-lg-4">
                    <div class="card bg-dark border-secondary h-100">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-activity text-danger me-2"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity" class="activity-feed">
                                <div class="text-center text-muted p-4">
                                    <i class="bi bi-clock-history fs-1 mb-3"></i>
                                    <div>No recent activity</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Commands Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning text-danger me-2"></i>
                                Quick Commands
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="quick-commands-grid">
                                <div class="quick-command-btn" onclick="executeQuickCommand('whoami')">
                                    <i class="bi bi-person-badge"></i>
                                    <div>Get User Info</div>
                                    <small class="text-muted">whoami</small>
                                </div>
                                <div class="quick-command-btn" onclick="executeQuickCommand('hostname')">
                                    <i class="bi bi-pc-display"></i>
                                    <div>Get Hostname</div>
                                    <small class="text-muted">hostname</small>
                                </div>
                                <div class="quick-command-btn" onclick="executeQuickCommand('ipconfig')">
                                    <i class="bi bi-router"></i>
                                    <div>Network Info</div>
                                    <small class="text-muted">ipconfig</small>
                                </div>
                                <div class="quick-command-btn" onclick="executeQuickCommand('tasklist')">
                                    <i class="bi bi-list-task"></i>
                                    <div>Running Processes</div>
                                    <small class="text-muted">tasklist</small>
                                </div>
                                <div class="quick-command-btn" onclick="executeQuickCommand('systeminfo')">
                                    <i class="bi bi-info-circle"></i>
                                    <div>System Info</div>
                                    <small class="text-muted">systeminfo</small>
                                </div>
                                <div class="quick-command-btn" onclick="executeQuickCommand('dir')">
                                    <i class="bi bi-folder"></i>
                                    <div>List Directory</div>
                                    <small class="text-muted">dir</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Management Page -->
        <div class="page-content" id="clientsPage">
            <div class="page-header">
                <h1><i class="bi bi-people me-3"></i>Client Management</h1>
                <p>View and manage all connected clients</p>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-broadcast text-danger me-2"></i>
                                    Connected Clients
                                </h5>
                                <button class="btn btn-outline-light btn-sm" onclick="refreshClients()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="clientsTable" class="table-responsive">
                                <table class="table table-dark table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Hostname</th>
                                            <th>Operating System</th>
                                            <th>Public IP</th>
                                            <th>Username</th>
                                            <th>Connected At</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="clientsTableBody">
                                        <tr>
                                            <td colspan="8" class="text-center text-muted p-4">
                                                <div class="spinner-border spinner-border-sm text-danger me-2" role="status"></div>
                                                Loading clients...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Command Center Page -->
        <div class="page-content" id="commandsPage">
            <div class="page-header">
                <h1><i class="bi bi-terminal me-3"></i>Command Center</h1>
                <p>Execute commands on connected clients</p>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning text-danger me-2"></i>
                                Command Execution
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="input-group">
                                        <span class="input-group-text bg-black border-secondary text-white">
                                            <i class="bi bi-terminal"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control bg-dark border-secondary text-white"
                                               id="commandInput"
                                               placeholder="Enter command (e.g., whoami, dir, ls -la)"
                                               onkeypress="handleKeyPress(event)">
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group">
                                        <button class="btn btn-danger" onclick="sendCommand()">
                                            <i class="bi bi-send me-1"></i>Send to Selected
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="sendToAll()">
                                            <i class="bi bi-broadcast me-1"></i>Send to All
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-dark border-secondary mb-0">
                                <strong>Selected Target:</strong>
                                <span id="selectedTarget" class="text-danger">None</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Command Output -->
            <div class="row">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-clipboard-data text-danger me-2"></i>
                                Command Output
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="output-area" id="outputArea"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Logs Page -->
        <div class="page-content" id="logsPage">
            <div class="page-header">
                <h1><i class="bi bi-journal-text me-3"></i>Activity Logs</h1>
                <p>Monitor system activity and command history</p>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-clock-history text-danger me-2"></i>
                                    System Logs
                                </h5>
                                <div class="btn-group">
                                    <button class="btn btn-outline-light btn-sm" onclick="clearLogs()">
                                        <i class="bi bi-trash me-1"></i>Clear
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="refreshLogs()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="logs-area" id="logsArea">
                                <div class="text-center text-muted p-4">
                                    <i class="bi bi-journal-text fs-1 mb-3"></i>
                                    <div>No logs available</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Page -->
        <div class="page-content" id="settingsPage">
            <div class="page-header">
                <h1><i class="bi bi-gear me-3"></i>Settings</h1>
                <p>Configure server settings and preferences</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-server text-danger me-2"></i>
                                Server Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Server IP</label>
                                <input type="text" class="form-control" value="0.0.0.0" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Server Port</label>
                                <input type="number" class="form-control" value="2710" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Web Interface Port</label>
                                <input type="number" class="form-control" value="1488" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-black border-danger">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-palette text-danger me-2"></i>
                                Interface Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Auto-refresh Interval (seconds)</label>
                                <input type="number" class="form-control" value="5" min="1" max="60">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Max Log Entries</label>
                                <input type="number" class="form-control" value="1000" min="100" max="10000">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        Enable Notifications
                                    </label>
                                </div>
                            </div>
                            <button class="btn btn-danger">
                                <i class="bi bi-check-lg me-1"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS for World Map -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Leaflet MarkerCluster JS -->
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

    <!-- Custom JavaScript -->
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/navigation.js"></script>
    <script src="/static/js/worldmap.js"></script>
</body>
</html>