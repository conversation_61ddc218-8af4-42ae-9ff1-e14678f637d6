/**
 * Navigation and Page Management for C2 Dashboard
 * Handles sidebar navigation, page switching, and mobile responsiveness
 */

// Global navigation state
let currentPage = 'dashboard';
let sidebarCollapsed = false;

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    setupNavigationEventListeners();
    handleResponsiveNavigation();
});

/**
 * Initialize navigation system
 */
function initializeNavigation() {
    // Set initial active page
    showPage('dashboard');
    
    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.page) {
            showPage(event.state.page, false);
        }
    });
    
    // Set initial browser state
    history.replaceState({ page: 'dashboard' }, '', '#dashboard');
}

/**
 * Setup event listeners for navigation
 */
function setupNavigationEventListeners() {
    // Sidebar navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            showPage(page);
        });
    });
    
    // Mobile sidebar toggle
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', toggleMobileSidebar);
    }
    
    // Sidebar close button (mobile)
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleMobileSidebar);
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('sidebar');
        const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
        
        if (window.innerWidth <= 768 && 
            !sidebar.contains(e.target) && 
            !mobileSidebarToggle.contains(e.target) &&
            sidebar.classList.contains('show')) {
            toggleMobileSidebar();
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', handleResponsiveNavigation);
}

/**
 * Show a specific page
 */
function showPage(pageId, updateHistory = true) {
    // Hide all pages
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    const targetPage = document.getElementById(pageId + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
        currentPage = pageId;
        
        // Update navigation active state
        updateNavigationState(pageId);
        
        // Update browser history
        if (updateHistory) {
            history.pushState({ page: pageId }, '', '#' + pageId);
        }
        
        // Update page title
        updatePageTitle(pageId);
        
        // Trigger page-specific initialization
        initializePage(pageId);
        
        // Close mobile sidebar if open
        if (window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.remove('show');
        }
    }
}

/**
 * Update navigation active state
 */
function updateNavigationState(activePageId) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-page') === activePageId) {
            link.classList.add('active');
        }
    });
}

/**
 * Update page title based on current page
 */
function updatePageTitle(pageId) {
    const titles = {
        'dashboard': 'SS NETWORK - Dashboard',
        'clients': 'SS NETWORK - Client Management',
        'commands': 'SS NETWORK - Command Center',
        'logs': 'SS NETWORK - Activity Logs',
        'settings': 'SS NETWORK - Settings'
    };
    
    document.title = titles[pageId] || 'SS NETWORK - C2 Dashboard';
}

/**
 * Initialize page-specific functionality
 */
function initializePage(pageId) {
    switch (pageId) {
        case 'dashboard':
            initializeDashboard();
            break;
        case 'clients':
            initializeClientsPage();
            break;
        case 'commands':
            initializeCommandsPage();
            break;
        case 'logs':
            initializeLogsPage();
            break;
        case 'settings':
            initializeSettingsPage();
            break;
    }
}

/**
 * Toggle mobile sidebar
 */
function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

/**
 * Handle responsive navigation
 */
function handleResponsiveNavigation() {
    const sidebar = document.getElementById('sidebar');
    
    if (window.innerWidth <= 768) {
        // Mobile: Hide sidebar by default
        sidebar.classList.remove('show');
    } else {
        // Desktop: Show sidebar
        sidebar.classList.remove('show');
    }
}

/**
 * Toggle sidebar collapse (desktop)
 */
function toggleSidebarCollapse() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
    sidebarCollapsed = !sidebarCollapsed;
    
    // Save preference
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
}

/**
 * Page-specific initialization functions
 */
function initializeDashboard() {
    console.log('Initializing dashboard page...');

    // Initialize world map with multiple retries
    let mapInitAttempts = 0;
    const maxAttempts = 5;

    function tryInitializeMap() {
        mapInitAttempts++;
        console.log(`Map initialization attempt ${mapInitAttempts}/${maxAttempts}`);

        const mapContainer = document.getElementById('worldMap');
        if (mapContainer && mapContainer.offsetWidth > 0 && typeof initializeWorldMap === 'function') {
            console.log('Map container ready, initializing...');
            initializeWorldMap();
        } else if (mapInitAttempts < maxAttempts) {
            console.log('Map container not ready, retrying in 500ms...');
            setTimeout(tryInitializeMap, 500);
        } else {
            console.warn('Failed to initialize map after', maxAttempts, 'attempts');
        }
    }

    // Start map initialization
    setTimeout(tryInitializeMap, 100);

    // Update dashboard stats
    if (typeof updateDashboardStats === 'function') {
        updateDashboardStats();
    }

    // Start periodic updates
    if (typeof startPeriodicUpdates === 'function') {
        startPeriodicUpdates();
    }
}

function initializeClientsPage() {
    // Refresh clients list
    if (typeof refreshClients === 'function') {
        refreshClients();
    }
}

function initializeCommandsPage() {
    // Focus on command input
    const commandInput = document.getElementById('commandInput');
    if (commandInput) {
        commandInput.focus();
    }
}

function initializeLogsPage() {
    // Load recent logs
    if (typeof refreshLogs === 'function') {
        refreshLogs();
    }
}

function initializeSettingsPage() {
    // Load current settings
    loadSettings();
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats() {
    // This will be called by the main dashboard.js
    if (typeof updateServerStatus === 'function') {
        updateServerStatus();
    }
}

/**
 * Load settings from localStorage or server
 */
function loadSettings() {
    // Load saved preferences
    const autoRefresh = localStorage.getItem('autoRefreshInterval') || '5';
    const maxLogs = localStorage.getItem('maxLogEntries') || '1000';
    const notifications = localStorage.getItem('enableNotifications') !== 'false';
    
    // Update form fields
    const autoRefreshInput = document.querySelector('input[type="number"][value="5"]');
    const maxLogsInput = document.querySelector('input[type="number"][value="1000"]');
    const notificationsCheckbox = document.getElementById('enableNotifications');
    
    if (autoRefreshInput) autoRefreshInput.value = autoRefresh;
    if (maxLogsInput) maxLogsInput.value = maxLogs;
    if (notificationsCheckbox) notificationsCheckbox.checked = notifications;
}

/**
 * Get current page
 */
function getCurrentPage() {
    return currentPage;
}

/**
 * Navigate to specific page programmatically
 */
function navigateToPage(pageId) {
    showPage(pageId);
}

/**
 * Quick command execution from dashboard
 */
function executeQuickCommand(command) {
    // Switch to commands page
    showPage('commands');
    
    // Set command in input
    setTimeout(() => {
        const commandInput = document.getElementById('commandInput');
        if (commandInput) {
            commandInput.value = command;
            commandInput.focus();
        }
    }, 100);
}

// Export functions for global access
window.showPage = showPage;
window.getCurrentPage = getCurrentPage;
window.navigateToPage = navigateToPage;
window.executeQuickCommand = executeQuickCommand;
window.toggleSidebarCollapse = toggleSidebarCollapse;
